import 'dotenv/config';
import { config } from 'dotenv';
import { handleSignInUser } from './src/auth/handler';
import { User } from 'next-auth';
import { Account } from 'next-auth';

// Load environment variables
config({ path: '.env.development' });
config({ path: '.env.local' });
config({ path: '.env' });

async function testRealLoginFlow() {
  try {
    console.log('Testing real Google login flow...');
    
    // Simulate the exact data that would come from Google OAuth
    const mockUser: User = {
      id: '*********',
      name: 'Test User Real',
      email: '<EMAIL>',
      image: 'https://lh3.googleusercontent.com/a/default-user=s96-c',
      emailVerified: new Date(),
    };

    const mockAccount: Account = {
      type: 'oauth',
      provider: 'google',
      providerAccountId: '*********',
      access_token: 'mock_access_token',
      expires_at: Math.floor(Date.now() / 1000) + 3600,
      token_type: 'Bearer',
      scope: 'openid email profile',
      id_token: 'mock_id_token',
    };

    console.log('Mock user:', mockUser);
    console.log('Mock account:', mockAccount);
    
    // This should trigger the exact same flow as a real Google login
    const result = await handleSignInUser(mockUser, mockAccount);
    
    console.log('✅ Real login flow completed successfully!');
    console.log('Result:', result);
    
  } catch (error) {
    console.error('❌ Error during real login flow test:', error);
    console.error('Error details:', error.message);
    console.error('Stack trace:', error.stack);
  }
  
  process.exit(0);
}

testRealLoginFlow();
