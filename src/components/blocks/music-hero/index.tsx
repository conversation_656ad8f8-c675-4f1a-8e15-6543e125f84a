"use client";

import { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Music, Play, Sparkles, Zap, Clock, Download } from "lucide-react";
import { <PERSON> } from "@/i18n/navigation";
import MusicForm from "@/components/music/generator/music-form";
import TrackCard from "@/components/music/track/track-card";

// Mock data for featured tracks
const FEATURED_TRACKS = [
  {
    uuid: "demo-1",
    title: "Upbeat Electronic Loop",
    prompt: "Energetic electronic music with driving beats",
    style: "electronic",
    mood: "upbeat",
    bpm: 128,
    duration: 30,
    file_url: "/demo/track1.mp3",
    download_count: 1250,
    is_public: true,
    created_at: "2024-01-15T10:00:00Z",
    waveform_data: {
      peaks: Array.from({ length: 100 }, (_, i) => {
        // Generate deterministic peaks to avoid hydration mismatch
        const seed = (i * 11 + 7) % 100;
        return Math.sin(seed * 0.15) * 0.4 + 0.5;
      }),
      duration: 30,
      sample_rate: 44100,
    },
    loop_verification: {
      is_seamless: true,
      verification_score: 0.95,
    },
  },
  {
    uuid: "demo-2",
    title: "Chill Ambient Background",
    prompt: "Relaxing ambient music for focus and productivity",
    style: "ambient",
    mood: "calm",
    bpm: 85,
    duration: 60,
    file_url: "/demo/track2.mp3",
    download_count: 890,
    is_public: true,
    created_at: "2024-01-14T15:30:00Z",
    waveform_data: {
      peaks: Array.from({ length: 100 }, (_, i) => {
        // Generate deterministic peaks to avoid hydration mismatch
        const seed = (i * 7 + 13) % 100;
        return (Math.sin(seed * 0.1) * 0.3 + 0.4) * 0.7;
      }),
      duration: 60,
      sample_rate: 44100,
    },
    loop_verification: {
      is_seamless: true,
      verification_score: 0.88,
    },
  },
];

export default function MusicHero() {
  const [showGenerator, setShowGenerator] = useState(false);

  const handleGenerateMusic = async (values: any) => {
    console.log("Generate music:", values);
    // TODO: Implement actual music generation
  };

  return (
    <section className="relative py-24 overflow-hidden">
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-secondary/5" />
      
      <div className="container relative">
        <div className="text-center mb-16">
          {/* Announcement badge */}
          <div className="flex items-center justify-center mb-8">
            <Badge variant="secondary" className="px-4 py-2 text-sm">
              <Sparkles className="mr-2 h-4 w-4" />
              AI-Powered Music Generation
            </Badge>
          </div>

          {/* Main heading */}
          <h1 className="mx-auto mb-6 max-w-4xl text-balance text-4xl font-bold lg:text-6xl">
            Create Perfect{" "}
            <span className="bg-gradient-to-r from-primary via-primary to-secondary bg-clip-text text-transparent">
              Loop Music
            </span>{" "}
            in Seconds
          </h1>

          {/* Description */}
          <p className="mx-auto max-w-2xl text-lg text-muted-foreground mb-8">
            Generate seamless, high-quality music loops for your videos, podcasts, 
            games, and creative projects. Powered by advanced AI technology.
          </p>

          {/* CTA buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
            <Button 
              size="lg" 
              onClick={() => setShowGenerator(true)}
              className="px-8"
            >
              <Music className="mr-2 h-5 w-5" />
              Start Creating Music
            </Button>
            <Link href="/explore">
              <Button variant="outline" size="lg" className="px-8">
                <Play className="mr-2 h-5 w-5" />
                Explore Examples
              </Button>
            </Link>
          </div>

          {/* Key features */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-3xl mx-auto">
            <div className="flex items-center gap-3 text-sm text-muted-foreground">
              <div className="p-2 bg-primary/10 rounded-full">
                <Zap className="h-4 w-4 text-primary" />
              </div>
              <span>Generate in 30 seconds</span>
            </div>
            <div className="flex items-center gap-3 text-sm text-muted-foreground">
              <div className="p-2 bg-primary/10 rounded-full">
                <Clock className="h-4 w-4 text-primary" />
              </div>
              <span>Perfect seamless loops</span>
            </div>
            <div className="flex items-center gap-3 text-sm text-muted-foreground">
              <div className="p-2 bg-primary/10 rounded-full">
                <Download className="h-4 w-4 text-primary" />
              </div>
              <span>Commercial use license</span>
            </div>
          </div>
        </div>

        {/* Music generator form */}
        {showGenerator && (
          <div className="flex justify-center mb-16">
            <MusicForm 
              onSubmit={handleGenerateMusic}
              userCredits={10} // TODO: Get from user context
            />
          </div>
        )}

        {/* Featured tracks */}
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-8">
            <h2 className="text-2xl font-bold mb-2">Featured Music Loops</h2>
            <p className="text-muted-foreground">
              Discover what's possible with LoopCraft AI
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {FEATURED_TRACKS.map((track) => (
              <TrackCard
                key={track.uuid}
                track={track}
                showUser={false}
                onPlay={(track) => console.log("Play:", track)}
                onDownload={(track) => console.log("Download:", track)}
                onLike={(track) => console.log("Like:", track)}
                onShare={(track) => console.log("Share:", track)}
              />
            ))}
          </div>

          <div className="text-center mt-8">
            <Link href="/explore">
              <Button variant="outline">
                View All Examples
              </Button>
            </Link>
          </div>
        </div>

        {/* Stats section */}
        <div className="mt-24 grid grid-cols-2 md:grid-cols-4 gap-8 max-w-2xl mx-auto">
          <div className="text-center">
            <div className="text-3xl font-bold text-primary mb-1">50K+</div>
            <div className="text-sm text-muted-foreground">Tracks Generated</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-primary mb-1">10K+</div>
            <div className="text-sm text-muted-foreground">Happy Creators</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-primary mb-1">95%</div>
            <div className="text-sm text-muted-foreground">Loop Quality</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-primary mb-1">24/7</div>
            <div className="text-sm text-muted-foreground">AI Available</div>
          </div>
        </div>

        {/* Trust indicators */}
        <div className="mt-16 text-center">
          <p className="text-sm text-muted-foreground mb-6">
            Trusted by content creators worldwide
          </p>
          <div className="flex justify-center items-center gap-8 opacity-60">
            {/* Placeholder for brand logos */}
            <div className="h-8 w-20 bg-muted rounded" />
            <div className="h-8 w-20 bg-muted rounded" />
            <div className="h-8 w-20 bg-muted rounded" />
            <div className="h-8 w-20 bg-muted rounded" />
          </div>
        </div>
      </div>
    </section>
  );
}
