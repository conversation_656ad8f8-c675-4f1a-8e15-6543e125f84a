"use client";

import { <PERSON>, Lo<PERSON>, Music, Crown, Zap, Star } from "lucide-react";
import { PricingItem, Pricing as PricingType } from "@/types/blocks/pricing";
import { useEffect, useState } from "react";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { loadStripe } from "@stripe/stripe-js";
import { toast } from "sonner";
import { useAppContext } from "@/contexts/app";
import { cn } from "@/lib/utils";

export default function MusicPricing({ pricing }: { pricing: PricingType }) {
  if (pricing.disabled) {
    return null;
  }

  const { user, setShowSignModal } = useAppContext();
  const [isLoading, setIsLoading] = useState(false);
  const [productId, setProductId] = useState<string | null>(null);

  const handleCheckout = async (item: PricingItem, cn_pay: boolean = false) => {
    try {
      if (!user && item.product_id !== "free") {
        setShowSignModal(true);
        return;
      }

      // Handle free plan
      if (item.product_id === "free") {
        if (!user) {
          setShowSignModal(true);
          return;
        }
        toast.success("You're already on the free plan!");
        return;
      }

      const params = {
        product_id: item.product_id,
        product_name: item.product_name,
        credits: item.credits,
        interval: item.interval,
        amount: cn_pay ? item.cn_amount : item.amount,
        currency: cn_pay ? "cny" : item.currency,
        valid_months: item.valid_months,
      };

      setIsLoading(true);
      setProductId(item.product_id);

      const response = await fetch("/api/checkout", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(params),
      });

      const data = await response.json();

      if (data.code === 0) {
        const stripe = await loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);
        if (stripe && data.data.session_id) {
          await stripe.redirectToCheckout({
            sessionId: data.data.session_id,
          });
        }
      } else {
        toast.error(data.message || "Payment failed");
      }
    } catch (error) {
      console.error("Checkout error:", error);
      toast.error("Payment failed. Please try again.");
    } finally {
      setIsLoading(false);
      setProductId(null);
    }
  };

  const getPlanIcon = (productId: string) => {
    switch (productId) {
      case "free":
        return <Music className="h-6 w-6" />;
      case "professional":
        return <Crown className="h-6 w-6" />;
      case "credits_pack":
        return <Zap className="h-6 w-6" />;
      default:
        return <Star className="h-6 w-6" />;
    }
  };

  const getPlanColor = (productId: string) => {
    switch (productId) {
      case "free":
        return "text-blue-600 border-blue-200 bg-blue-50";
      case "professional":
        return "text-purple-600 border-purple-200 bg-purple-50";
      case "credits_pack":
        return "text-orange-600 border-orange-200 bg-orange-50";
      default:
        return "text-gray-600 border-gray-200 bg-gray-50";
    }
  };

  return (
    <section id={pricing.name} className="py-16 bg-gradient-to-b from-background to-muted/20">
      <div className="container">
        {/* Header */}
        <div className="mx-auto mb-16 text-center max-w-3xl">
          <div className="flex items-center justify-center mb-4">
            <Badge variant="secondary" className="px-4 py-2 text-sm">
              <Music className="mr-2 h-4 w-4" />
              Pricing Plans
            </Badge>
          </div>
          
          <h2 className="mb-6 text-4xl font-bold lg:text-5xl">
            {pricing.title}
          </h2>
          
          <p className="text-lg text-muted-foreground">
            {pricing.description}
          </p>
        </div>

        {/* Pricing Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {(pricing.items || []).map((item, index) => (
            <Card 
              key={index} 
              className={cn(
                "relative transition-all duration-300 hover:shadow-lg",
                item.is_featured && "ring-2 ring-primary shadow-lg scale-105"
              )}
            >
              {/* Featured Badge */}
              {item.is_featured && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <Badge className="px-4 py-1 bg-primary text-primary-foreground">
                    {item.label}
                  </Badge>
                </div>
              )}

              <CardHeader className="text-center pb-4">
                {/* Plan Icon */}
                <div className={cn(
                  "w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center",
                  getPlanColor(item.product_id)
                )}>
                  {getPlanIcon(item.product_id)}
                </div>

                <CardTitle className="text-2xl font-bold">{item.title}</CardTitle>
                <CardDescription className="text-base">{item.description}</CardDescription>

                {/* Price */}
                <div className="mt-4">
                  <div className="flex items-baseline justify-center gap-1">
                    <span className="text-4xl font-bold">{item.price}</span>
                    {item.unit && item.unit !== "forever" && (
                      <span className="text-muted-foreground">/{item.unit}</span>
                    )}
                  </div>
                  
                  {item.original_price && (
                    <div className="text-sm text-muted-foreground line-through">
                      {item.original_price}
                    </div>
                  )}
                  
                  {item.tip && (
                    <div className="text-sm text-primary font-medium mt-2">
                      {item.tip}
                    </div>
                  )}
                </div>
              </CardHeader>

              <CardContent className="px-6">
                {/* Features */}
                <div className="space-y-3">
                  <h4 className="font-semibold text-sm text-muted-foreground uppercase tracking-wide">
                    {item.features_title}
                  </h4>
                  
                  <ul className="space-y-3">
                    {(item.features || []).map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-start gap-3">
                        <Check className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                        <span className="text-sm">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </CardContent>

              <CardFooter className="px-6 pt-4">
                <Button
                  className="w-full"
                  variant={item.is_featured ? "default" : "outline"}
                  size="lg"
                  onClick={() => handleCheckout(item)}
                  disabled={isLoading && productId === item.product_id}
                >
                  {isLoading && productId === item.product_id ? (
                    <>
                      <Loader className="mr-2 h-4 w-4 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    <>
                      {item.button.icon && (
                        <span className="mr-2">
                          {item.button.icon === "RiMusicLine" && <Music className="h-4 w-4" />}
                          {item.button.icon === "RiVipCrownLine" && <Crown className="h-4 w-4" />}
                          {item.button.icon === "RiCoinLine" && <Zap className="h-4 w-4" />}
                        </span>
                      )}
                      {item.button.title}
                    </>
                  )}
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>

        {/* FAQ or Additional Info */}
        <div className="mt-16 text-center">
          <div className="max-w-2xl mx-auto">
            <h3 className="text-xl font-semibold mb-4">All plans include</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-muted-foreground">
              <div className="flex items-center justify-center gap-2">
                <Check className="h-4 w-4 text-primary" />
                <span>Commercial licensing</span>
              </div>
              <div className="flex items-center justify-center gap-2">
                <Check className="h-4 w-4 text-primary" />
                <span>Loop quality verification</span>
              </div>
              <div className="flex items-center justify-center gap-2">
                <Check className="h-4 w-4 text-primary" />
                <span>Seamless loop guarantee</span>
              </div>
            </div>
          </div>
        </div>

        {/* Money Back Guarantee */}
        <div className="mt-12 text-center">
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-green-50 text-green-700 rounded-full text-sm">
            <Check className="h-4 w-4" />
            <span>30-day money-back guarantee on all paid plans</span>
          </div>
        </div>
      </div>
    </section>
  );
}
