"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>eader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { 
  Play, 
  Pause, 
  Download, 
  Heart, 
  Share2, 
  MoreHorizontal,
  Clock,
  Music,
  Verified
} from "lucide-react";
import { cn } from "@/lib/utils";
import { SimpleWaveform } from "../waveform/waveform-display";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface Track {
  uuid: string;
  title?: string;
  slug?: string;
  prompt: string;
  style?: string;
  mood?: string;
  bpm?: number;
  duration: number;
  file_url: string;
  file_size?: number;
  download_count: number;
  is_public: boolean;
  created_at: string;
  waveform_data?: {
    peaks: number[];
    duration: number;
    sample_rate: number;
  };
  user?: {
    name?: string;
    email?: string;
    image?: string;
  };
  loop_verification?: {
    is_seamless: boolean;
    verification_score: number;
  };
}

interface TrackCardProps {
  track: Track;
  isPlaying?: boolean;
  currentTime?: number;
  showUser?: boolean;
  showWaveform?: boolean;
  compact?: boolean;
  className?: string;
  onPlay?: (track: Track) => void;
  onPause?: () => void;
  onDownload?: (track: Track) => void;
  onLike?: (track: Track) => void;
  onShare?: (track: Track) => void;
  onAddToCollection?: (track: Track) => void;
}

export default function TrackCard({
  track,
  isPlaying = false,
  currentTime = 0,
  showUser = true,
  showWaveform = true,
  compact = false,
  className,
  onPlay,
  onPause,
  onDownload,
  onLike,
  onShare,
  onAddToCollection,
}: TrackCardProps) {
  const [isLiked, setIsLiked] = useState(false);

  const handlePlayPause = () => {
    if (isPlaying) {
      onPause?.();
    } else {
      onPlay?.(track);
    }
  };

  const handleLike = () => {
    setIsLiked(!isLiked);
    onLike?.(track);
  };

  const handleDownload = () => {
    onDownload?.(track);
  };

  const handleShare = () => {
    onShare?.(track);
  };

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return "";
    const mb = bytes / (1024 * 1024);
    return `${mb.toFixed(1)} MB`;
  };

  const getInitials = (name?: string, email?: string) => {
    if (name) {
      return name.split(" ").map(n => n[0]).join("").toUpperCase();
    }
    if (email) {
      return email[0].toUpperCase();
    }
    return "U";
  };

  return (
    <Card className={cn("group hover:shadow-md transition-shadow", className)}>
      <CardHeader className={cn("pb-3", compact && "pb-2")}>
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <h3 className="font-medium text-sm truncate">
                {track.title || "Untitled Track"}
              </h3>
              {track.loop_verification?.is_seamless && (
                <Verified className="h-4 w-4 text-green-500 shrink-0" />
              )}
            </div>
            
            {!compact && (
              <p className="text-xs text-muted-foreground line-clamp-2 mb-2">
                {track.prompt}
              </p>
            )}

            <div className="flex flex-wrap gap-1">
              {track.style && (
                <Badge variant="secondary" className="text-xs">
                  {track.style}
                </Badge>
              )}
              {track.mood && (
                <Badge variant="outline" className="text-xs">
                  {track.mood}
                </Badge>
              )}
              {track.bpm && (
                <Badge variant="outline" className="text-xs">
                  {track.bpm} BPM
                </Badge>
              )}
            </div>
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onAddToCollection?.(track)}>
                Add to Collection
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleShare}>
                <Share2 className="mr-2 h-4 w-4" />
                Share
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleDownload}>
                <Download className="mr-2 h-4 w-4" />
                Download
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>

      <CardContent className={cn("py-3", compact && "py-2")}>
        {/* Waveform */}
        {showWaveform && track.waveform_data && (
          <div className="mb-3">
            <SimpleWaveform
              peaks={track.waveform_data.peaks}
              currentTime={currentTime}
              duration={track.duration}
              height={compact ? 32 : 48}
              interactive={true}
              onSeek={(time) => {
                // Handle seek if needed
                console.log("Seek to:", time);
              }}
            />
          </div>
        )}

        {/* Controls */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Button
              variant="default"
              size="sm"
              onClick={handlePlayPause}
              className="h-8 w-8 rounded-full p-0"
            >
              {isPlaying ? (
                <Pause className="h-4 w-4" />
              ) : (
                <Play className="h-4 w-4" />
              )}
            </Button>

            <div className="flex items-center gap-1 text-xs text-muted-foreground">
              <Clock className="h-3 w-3" />
              <span>{formatDuration(track.duration)}</span>
            </div>
          </div>

          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleLike}
              className={cn(
                "h-8 w-8 p-0",
                isLiked && "text-red-500 hover:text-red-600"
              )}
            >
              <Heart className={cn("h-4 w-4", isLiked && "fill-current")} />
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={handleDownload}
              className="h-8 w-8 p-0"
            >
              <Download className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardContent>

      {(showUser || !compact) && (
        <CardFooter className="pt-0 pb-3">
          <div className="flex items-center justify-between w-full text-xs text-muted-foreground">
            {showUser && track.user && (
              <div className="flex items-center gap-2">
                <Avatar className="h-6 w-6">
                  <AvatarFallback className="text-xs">
                    {getInitials(track.user.name, track.user.email)}
                  </AvatarFallback>
                </Avatar>
                <span>{track.user.name || track.user.email}</span>
              </div>
            )}

            <div className="flex items-center gap-3 ml-auto">
              {track.download_count > 0 && (
                <div className="flex items-center gap-1">
                  <Download className="h-3 w-3" />
                  <span>{track.download_count}</span>
                </div>
              )}
              
              {track.file_size && (
                <span>{formatFileSize(track.file_size)}</span>
              )}

              {track.loop_verification && (
                <div className="flex items-center gap-1">
                  <Music className="h-3 w-3" />
                  <span>{Math.round(track.loop_verification.verification_score * 100)}%</span>
                </div>
              )}
            </div>
          </div>
        </CardFooter>
      )}
    </Card>
  );
}
