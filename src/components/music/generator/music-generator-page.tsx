"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { TechCard, TechCardContent, TechCardHeader, TechCardTitle } from "@/components/ui/tech-card";
import { Button } from "@/components/ui/button";
import { TechButton } from "@/components/ui/tech-button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import {
  Music,
  Sparkles,
  Clock,
  Zap,
  CheckCircle,
  AlertCircle,
  Loader2,
  Play,
  Download,
  History
} from "lucide-react";
import { cn } from "@/lib/utils";
import { toast } from "sonner";
import { Track } from "@/types/music";
import { generateTrackUrl } from "@/lib/track-slug";
import Link from "next/link";
import { useRouter } from "next/navigation";
import MusicForm from "./music-form";
import AudioPlayer from "../player/audio-player";
import LoopTestPlayer from "../player/loop-test-player";

interface MusicGeneratorPageProps {
  locale: string;
}

interface GenerationResult {
  generation_uuid: string;
  uuid?: string; // For database records
  prompt?: string;
  style?: string;
  mood?: string;
  bpm?: number;
  duration?: number;
  status: "pending" | "processing" | "completed" | "failed";
  estimated_completion_time?: number;
  track?: Track;
  error?: string;
  created_at?: string | Date;
}

export default function MusicGeneratorPage({ locale }: MusicGeneratorPageProps) {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [userCredits, setUserCredits] = useState(0);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState(0);
  const [currentGeneration, setCurrentGeneration] = useState<GenerationResult | null>(null);
  const [recentGenerations, setRecentGenerations] = useState<GenerationResult[]>([]);

  useEffect(() => {
    // 只有在用户已登录时才加载数据
    if (status === "authenticated" && session?.user) {
      loadUserCredits();
      loadRecentGenerations();
    }
  }, [status, session]);

  const loadUserCredits = async () => {
    // 检查用户是否已登录
    if (status !== "authenticated" || !session?.user) {
      console.log("User not authenticated, skipping credits load");
      setUserCredits(0);
      return;
    }

    try {
      const response = await fetch("/api/user/credits");
      const data = await response.json();
      if (response.ok && data.success) {
        setUserCredits(data.credits || 0);
      } else if (response.status === 401) {
        console.log("Authentication required for user credits");
        setUserCredits(0);
      } else {
        console.error("Failed to load user credits:", data.error || "Unknown error");
      }
    } catch (error) {
      console.error("Failed to load user credits:", error);
    }
  };

  const loadRecentGenerations = async () => {
    // 检查用户是否已登录
    if (status !== "authenticated" || !session?.user) {
      console.log("User not authenticated, skipping recent generations load");
      return;
    }

    try {
      const response = await fetch("/api/music/generations?limit=5");
      const data = await response.json();

      if (response.ok && data.success) {
        // Transform database records to GenerationResult format
        const transformedGenerations = (data.generations || []).map((gen: any) => ({
          generation_uuid: gen.uuid,
          uuid: gen.uuid,
          prompt: gen.prompt,
          style: gen.style,
          mood: gen.mood,
          bpm: gen.bpm,
          duration: gen.duration,
          status: gen.status,
          created_at: gen.created_at,
          // Track will be loaded separately if needed
        }));
        setRecentGenerations(transformedGenerations);
      } else if (response.status === 401) {
        console.log("Authentication required for recent generations");
        // 不显示错误，因为这是正常的未登录状态
      } else {
        console.error("Failed to load recent generations:", data.error || "Unknown error");
      }
    } catch (error) {
      console.error("Failed to load recent generations:", error);
    }
  };

  const handleMusicGeneration = async (values: any) => {
    setIsGenerating(true);
    setGenerationProgress(0);

    try {
      const response = await fetch("/api/music/generate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          prompt: values.prompt,
          style: values.style,
          mood: values.mood,
          duration: parseInt(values.duration),
          bpm: values.bpmLocked ? values.bmp : undefined,
          provider: values.provider,
        }),
      });

      const data = await response.json();

      if (data.code === 0) {
        const generation: GenerationResult = {
          generation_uuid: data.data.generation_uuid,
          status: data.data.status,
          estimated_completion_time: data.data.estimated_completion_time,
        };

        setCurrentGeneration(generation);
        toast.success("Music generation started!");

        // Update user credits
        await loadUserCredits();

        // Poll for completion
        pollGenerationStatus(generation.generation_uuid, generation.estimated_completion_time || 60);
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      console.error("Music generation failed:", error);
      toast.error("Failed to start music generation");
      setIsGenerating(false);
    }
  };

  const pollGenerationStatus = async (generationUuid: string, estimatedTime: number) => {
    const startTime = Date.now();
    const pollInterval = 3000; // 3 seconds

    const poll = async () => {
      try {
        const response = await fetch(`/api/music/status?generation_uuid=${generationUuid}`);
        const data = await response.json();

        if (data.code === 0) {
          const status = data.data.status;

          if (status === "completed") {
            setCurrentGeneration(prev => prev ? {
              ...prev,
              status: "completed",
              track: data.data.track,
            } : null);
            setGenerationProgress(100);
            setIsGenerating(false);
            toast.success("Music generation completed!");
            
            // Reload recent generations
            await loadRecentGenerations();
            return;
          } else if (status === "failed") {
            setCurrentGeneration(prev => prev ? {
              ...prev,
              status: "failed",
              error: data.data.error || "Generation failed",
            } : null);
            setIsGenerating(false);
            toast.error("Music generation failed");
            return;
          }
        }

        // Update progress based on elapsed time
        const elapsed = Date.now() - startTime;
        const progress = Math.min((elapsed / (estimatedTime * 1000)) * 100, 95);
        setGenerationProgress(progress);

        // Continue polling
        setTimeout(poll, pollInterval);
      } catch (error) {
        console.error("Failed to poll generation status:", error);
        setTimeout(poll, pollInterval);
      }
    };

    poll();
  };

  const resetGeneration = () => {
    setCurrentGeneration(null);
    setIsGenerating(false);
    setGenerationProgress(0);
  };

  // 如果正在加载认证状态，显示加载指示器
  if (status === "loading") {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="flex items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2">Loading...</span>
          </div>
        </div>
      </div>
    );
  }

  // 如果用户未登录，显示登录提示
  if (status === "unauthenticated") {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="text-center space-y-6">
            <div className="flex items-center justify-center gap-2 mb-4">
              <div className="p-3 bg-primary/10 rounded-full">
                <Sparkles className="h-8 w-8 text-primary" />
              </div>
            </div>
            <h1 className="text-4xl font-bold tracking-tight">
              Generate AI Music Loops
            </h1>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Create seamless, professional-quality music loops for your content with AI.
              Perfect for videos, podcasts, games, and more.
            </p>

            <Card className="max-w-md mx-auto">
              <CardContent className="pt-6">
                <div className="text-center space-y-4">
                  <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto" />
                  <h3 className="text-lg font-semibold">Sign In Required</h3>
                  <p className="text-muted-foreground">
                    Please sign in to generate AI music loops and access your generation history.
                  </p>
                  <Button asChild className="w-full">
                    <a href="/auth/signin">Sign In</a>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background relative overflow-hidden">
      {/* Tech background */}
      <div className="absolute inset-0 tech-grid opacity-20" />
      <div className="absolute top-0 right-1/4 w-96 h-96 bg-primary/5 rounded-full blur-3xl" />
      <div className="absolute bottom-0 left-1/4 w-96 h-96 bg-neon-cyan/5 rounded-full blur-3xl" />

      <div className="container mx-auto py-12 px-4 relative z-10">
        <div className="max-w-6xl mx-auto space-y-12">
          {/* Header */}
          <div className="text-center space-y-6">
            <div className="inline-flex items-center gap-2 glass-card px-4 py-2 rounded-full mb-4">
              <Sparkles className="h-4 w-4 text-primary" />
              <span className="text-sm font-medium">AI Music Generator</span>
            </div>

            <h1 className="text-5xl font-bold tracking-tight lg:text-6xl">
              <span className="bg-gradient-to-r from-foreground via-primary to-foreground bg-clip-text text-transparent">
                Generate AI Music Loops
              </span>
            </h1>

            <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Create seamless, professional-quality music loops for your content with AI.
              Perfect for videos, podcasts, games, and more.
            </p>

            {/* Credits Display */}
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
              <div className="glass-card px-6 py-3 rounded-full">
                <div className="flex items-center gap-2">
                  <div className="w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center">
                    <Zap className="h-4 w-4 text-primary" />
                  </div>
                  <span className="font-semibold">{userCredits} Credits Available</span>
                </div>
              </div>
              <div className="glass-card px-6 py-3 rounded-full">
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm text-muted-foreground">15s = 1 • 30s = 2 • 60s = 3 credits</span>
                </div>
              </div>
            </div>
          </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Generation Form */}
          <div className="space-y-6">
            <MusicForm
              onSubmit={handleMusicGeneration}
              isLoading={isGenerating}
              userCredits={userCredits}
            />

            {/* Generation Progress */}
            {isGenerating && currentGeneration && (
              <TechCard variant="neon" glow={true} animated={true}>
                <TechCardHeader>
                  <TechCardTitle className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-full bg-primary/20 flex items-center justify-center">
                      <Loader2 className="h-5 w-5 animate-spin text-primary" />
                    </div>
                    <span className="bg-gradient-to-r from-primary to-neon-purple bg-clip-text text-transparent">
                      Generating Your Music Loop
                    </span>
                  </TechCardTitle>
                </TechCardHeader>
                <TechCardContent className="space-y-6">
                  <div className="relative">
                    <Progress value={generationProgress} className="w-full h-3" />
                    <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-neon-cyan/20 rounded-full opacity-50" />
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="flex items-center gap-2">
                      <div className="w-2 h-2 rounded-full bg-primary animate-pulse" />
                      Status: <span className="font-medium text-primary">{currentGeneration.status}</span>
                    </span>
                    <span className="font-bold text-lg">{Math.round(generationProgress)}%</span>
                  </div>
                  <div className="glass-card p-4 rounded-lg">
                    <p className="text-sm text-muted-foreground flex items-center gap-2">
                      <Clock className="h-4 w-4" />
                      This usually takes {currentGeneration.estimated_completion_time || 60} seconds.
                      Please don't close this page.
                    </p>
                  </div>
                </TechCardContent>
              </TechCard>
            )}

            {/* Generation Result */}
            {currentGeneration?.status === "completed" && currentGeneration.track && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-green-500" />
                    Generation Complete!
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <h3 className="font-medium">{currentGeneration.track.title}</h3>
                    <p className="text-sm text-muted-foreground">
                      {currentGeneration.track.prompt}
                    </p>
                    <div className="flex gap-2">
                      {currentGeneration.track.style && (
                        <Badge variant="secondary">{currentGeneration.track.style}</Badge>
                      )}
                      {currentGeneration.track.mood && (
                        <Badge variant="outline">{currentGeneration.track.mood}</Badge>
                      )}
                      {currentGeneration.track.bpm && (
                        <Badge variant="outline">{currentGeneration.track.bpm} BPM</Badge>
                      )}
                    </div>
                  </div>

                  <LoopTestPlayer
                    track={currentGeneration.track}
                    autoStart={false}
                    maxLoops={3}
                  />

                  <div className="flex gap-2">
                    <Button
                      onClick={() => {
                        const link = document.createElement("a");
                        link.href = currentGeneration.track!.file_url;
                        link.download = `${currentGeneration.track!.title}.mp3`;
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                      }}
                      className="flex-1"
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Download
                    </Button>
                    <Button
                      variant="outline"
                      onClick={resetGeneration}
                    >
                      Generate Another
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Generation Error */}
            {currentGeneration?.status === "failed" && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <AlertCircle className="h-5 w-5 text-red-500" />
                    Generation Failed
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-sm text-muted-foreground">
                    {currentGeneration.error || "Something went wrong during generation."}
                  </p>
                  <Button onClick={resetGeneration} variant="outline">
                    Try Again
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Recent Generations */}
          <div className="space-y-6">
            <TechCard variant="glass" animated={true}>
              <TechCardHeader>
                <TechCardTitle className="flex items-center gap-3">
                  <div className="w-10 h-10 rounded-full bg-muted flex items-center justify-center">
                    <History className="h-5 w-5 text-muted-foreground" />
                  </div>
                  Recent Generations
                </TechCardTitle>
              </TechCardHeader>
              <CardContent>
                {recentGenerations.length > 0 ? (
                  <div className="space-y-4">
                    {recentGenerations.map((generation) => {
                      const trackUrl = generation.track ? generateTrackUrl({
                        prompt: generation.prompt || generation.track.prompt,
                        title: generation.track.title,
                        bpm: generation.track.bpm || generation.bpm,
                        uuid: generation.track.uuid,
                        style: generation.track.style || generation.style,
                        slug: generation.track.slug
                      }) : null;

                      return (
                        <div
                          key={generation.generation_uuid}
                          className={cn(
                            "flex items-center justify-between p-3 border rounded-lg transition-colors",
                            generation.status === "completed" && trackUrl
                              ? "hover:bg-muted/50 cursor-pointer"
                              : ""
                          )}
                          onClick={() => {
                            if (generation.status === "completed" && trackUrl) {
                              router.push(trackUrl);
                            }
                          }}
                        >
                          <div className="space-y-1 flex-1">
                            <div className="font-medium text-sm">
                              {generation.track?.title || generation.prompt || "Untitled"}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {(generation.track?.style || generation.style) && `${generation.track?.style || generation.style} • `}
                              {(generation.track?.duration || generation.duration) && `${generation.track?.duration || generation.duration}s`}
                              {(generation.track?.bpm || generation.bpm) && ` • ${generation.track?.bpm || generation.bpm} BPM`}
                            </div>
                            {generation.status === "completed" && trackUrl && (
                              <div className="text-xs text-primary">
                                Click to view details →
                              </div>
                            )}
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge
                              variant={
                                generation.status === "completed"
                                  ? "default"
                                  : generation.status === "failed"
                                  ? "destructive"
                                  : "secondary"
                              }
                            >
                              {generation.status}
                            </Badge>
                            {generation.track && generation.status === "completed" && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  // TODO: Implement quick play functionality
                                  toast.info("Quick play coming soon!");
                                }}
                              >
                                <Play className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <Music className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No recent generations.</p>
                    <p className="text-sm">Start creating your first music loop!</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Tips Card */}
            <Card>
              <CardHeader>
                <CardTitle>💡 Generation Tips</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3 text-sm">
                <div>
                  <strong>Be specific:</strong> Describe the mood, instruments, and style you want.
                </div>
                <div>
                  <strong>Use BPM lock:</strong> For precise tempo matching with your content.
                </div>
                <div>
                  <strong>Test loops:</strong> Use the loop test player to verify seamless playback.
                </div>
                <div>
                  <strong>Choose duration:</strong> 15s for short clips, 60s for longer content.
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
