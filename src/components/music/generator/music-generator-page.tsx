"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import {
  Music,
  Sparkles,
  Clock,
  Zap,
  CheckCircle,
  AlertCircle,
  Loader2,
  Play,
  Download,
  History
} from "lucide-react";
import { cn } from "@/lib/utils";
import { toast } from "sonner";
import { Track } from "@/types/music";
import { generateTrackUrl } from "@/lib/track-slug";
import Link from "next/link";
import { useRouter } from "next/navigation";
import MusicForm from "./music-form";
import AudioPlayer from "../player/audio-player";
import LoopTestPlayer from "../player/loop-test-player";

interface MusicGeneratorPageProps {
  locale: string;
}

interface GenerationResult {
  generation_uuid: string;
  status: "pending" | "processing" | "completed" | "failed";
  estimated_completion_time?: number;
  track?: Track;
  error?: string;
}

export default function MusicGeneratorPage({ locale }: MusicGeneratorPageProps) {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [userCredits, setUserCredits] = useState(0);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState(0);
  const [currentGeneration, setCurrentGeneration] = useState<GenerationResult | null>(null);
  const [recentGenerations, setRecentGenerations] = useState<GenerationResult[]>([]);

  useEffect(() => {
    // 只有在用户已登录时才加载数据
    if (status === "authenticated" && session?.user) {
      loadUserCredits();
      loadRecentGenerations();
    }
  }, [status, session]);

  const loadUserCredits = async () => {
    // 检查用户是否已登录
    if (status !== "authenticated" || !session?.user) {
      console.log("User not authenticated, skipping credits load");
      setUserCredits(0);
      return;
    }

    try {
      const response = await fetch("/api/user/credits");
      const data = await response.json();
      if (response.ok && data.success) {
        setUserCredits(data.credits || 0);
      } else if (response.status === 401) {
        console.log("Authentication required for user credits");
        setUserCredits(0);
      } else {
        console.error("Failed to load user credits:", data.error || "Unknown error");
      }
    } catch (error) {
      console.error("Failed to load user credits:", error);
    }
  };

  const loadRecentGenerations = async () => {
    // 检查用户是否已登录
    if (status !== "authenticated" || !session?.user) {
      console.log("User not authenticated, skipping recent generations load");
      return;
    }

    try {
      const response = await fetch("/api/music/generations?limit=5");
      const data = await response.json();

      if (response.ok && data.success) {
        setRecentGenerations(data.generations || []);
      } else if (response.status === 401) {
        console.log("Authentication required for recent generations");
        // 不显示错误，因为这是正常的未登录状态
      } else {
        console.error("Failed to load recent generations:", data.error || "Unknown error");
      }
    } catch (error) {
      console.error("Failed to load recent generations:", error);
    }
  };

  const handleMusicGeneration = async (values: any) => {
    setIsGenerating(true);
    setGenerationProgress(0);

    try {
      const response = await fetch("/api/music/generate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          prompt: values.prompt,
          style: values.style,
          mood: values.mood,
          duration: parseInt(values.duration),
          bpm: values.bpmLocked ? values.bmp : undefined,
          provider: values.provider,
        }),
      });

      const data = await response.json();

      if (data.code === 0) {
        const generation: GenerationResult = {
          generation_uuid: data.data.generation_uuid,
          status: data.data.status,
          estimated_completion_time: data.data.estimated_completion_time,
        };

        setCurrentGeneration(generation);
        toast.success("Music generation started!");

        // Update user credits
        await loadUserCredits();

        // Poll for completion
        pollGenerationStatus(generation.generation_uuid, generation.estimated_completion_time || 60);
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      console.error("Music generation failed:", error);
      toast.error("Failed to start music generation");
      setIsGenerating(false);
    }
  };

  const pollGenerationStatus = async (generationUuid: string, estimatedTime: number) => {
    const startTime = Date.now();
    const pollInterval = 3000; // 3 seconds

    const poll = async () => {
      try {
        const response = await fetch(`/api/music/status?generation_uuid=${generationUuid}`);
        const data = await response.json();

        if (data.code === 0) {
          const status = data.data.status;

          if (status === "completed") {
            setCurrentGeneration(prev => prev ? {
              ...prev,
              status: "completed",
              track: data.data.track,
            } : null);
            setGenerationProgress(100);
            setIsGenerating(false);
            toast.success("Music generation completed!");
            
            // Reload recent generations
            await loadRecentGenerations();
            return;
          } else if (status === "failed") {
            setCurrentGeneration(prev => prev ? {
              ...prev,
              status: "failed",
              error: data.data.error || "Generation failed",
            } : null);
            setIsGenerating(false);
            toast.error("Music generation failed");
            return;
          }
        }

        // Update progress based on elapsed time
        const elapsed = Date.now() - startTime;
        const progress = Math.min((elapsed / (estimatedTime * 1000)) * 100, 95);
        setGenerationProgress(progress);

        // Continue polling
        setTimeout(poll, pollInterval);
      } catch (error) {
        console.error("Failed to poll generation status:", error);
        setTimeout(poll, pollInterval);
      }
    };

    poll();
  };

  const resetGeneration = () => {
    setCurrentGeneration(null);
    setIsGenerating(false);
    setGenerationProgress(0);
  };

  // 如果正在加载认证状态，显示加载指示器
  if (status === "loading") {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="flex items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2">Loading...</span>
          </div>
        </div>
      </div>
    );
  }

  // 如果用户未登录，显示登录提示
  if (status === "unauthenticated") {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="text-center space-y-6">
            <div className="flex items-center justify-center gap-2 mb-4">
              <div className="p-3 bg-primary/10 rounded-full">
                <Sparkles className="h-8 w-8 text-primary" />
              </div>
            </div>
            <h1 className="text-4xl font-bold tracking-tight">
              Generate AI Music Loops
            </h1>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Create seamless, professional-quality music loops for your content with AI.
              Perfect for videos, podcasts, games, and more.
            </p>

            <Card className="max-w-md mx-auto">
              <CardContent className="pt-6">
                <div className="text-center space-y-4">
                  <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto" />
                  <h3 className="text-lg font-semibold">Sign In Required</h3>
                  <p className="text-muted-foreground">
                    Please sign in to generate AI music loops and access your generation history.
                  </p>
                  <Button asChild className="w-full">
                    <a href="/auth/signin">Sign In</a>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-6xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <div className="flex items-center justify-center gap-2 mb-4">
            <div className="p-3 bg-primary/10 rounded-full">
              <Sparkles className="h-8 w-8 text-primary" />
            </div>
          </div>
          <h1 className="text-4xl font-bold tracking-tight">
            Generate AI Music Loops
          </h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Create seamless, professional-quality music loops for your content with AI.
            Perfect for videos, podcasts, games, and more.
          </p>

          {/* Credits Display */}
          <div className="flex items-center justify-center gap-4">
            <Badge variant="outline" className="px-4 py-2">
              <Zap className="h-4 w-4 mr-2" />
              {userCredits} Credits Available
            </Badge>
            <Badge variant="secondary" className="px-4 py-2">
              <Clock className="h-4 w-4 mr-2" />
              15s = 1 • 30s = 2 • 60s = 3 credits
            </Badge>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Generation Form */}
          <div className="space-y-6">
            <MusicForm
              onSubmit={handleMusicGeneration}
              isLoading={isGenerating}
              userCredits={userCredits}
            />

            {/* Generation Progress */}
            {isGenerating && currentGeneration && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Loader2 className="h-5 w-5 animate-spin" />
                    Generating Your Music Loop
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Progress value={generationProgress} className="w-full" />
                  <div className="flex items-center justify-between text-sm text-muted-foreground">
                    <span>Status: {currentGeneration.status}</span>
                    <span>{Math.round(generationProgress)}% complete</span>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    This usually takes {currentGeneration.estimated_completion_time || 60} seconds. 
                    Please don't close this page.
                  </p>
                </CardContent>
              </Card>
            )}

            {/* Generation Result */}
            {currentGeneration?.status === "completed" && currentGeneration.track && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-green-500" />
                    Generation Complete!
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <h3 className="font-medium">{currentGeneration.track.title}</h3>
                    <p className="text-sm text-muted-foreground">
                      {currentGeneration.track.prompt}
                    </p>
                    <div className="flex gap-2">
                      {currentGeneration.track.style && (
                        <Badge variant="secondary">{currentGeneration.track.style}</Badge>
                      )}
                      {currentGeneration.track.mood && (
                        <Badge variant="outline">{currentGeneration.track.mood}</Badge>
                      )}
                      {currentGeneration.track.bpm && (
                        <Badge variant="outline">{currentGeneration.track.bpm} BPM</Badge>
                      )}
                    </div>
                  </div>

                  <LoopTestPlayer
                    track={currentGeneration.track}
                    autoStart={false}
                    maxLoops={3}
                  />

                  <div className="flex gap-2">
                    <Button
                      onClick={() => {
                        const link = document.createElement("a");
                        link.href = currentGeneration.track!.file_url;
                        link.download = `${currentGeneration.track!.title}.mp3`;
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                      }}
                      className="flex-1"
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Download
                    </Button>
                    <Button
                      variant="outline"
                      onClick={resetGeneration}
                    >
                      Generate Another
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Generation Error */}
            {currentGeneration?.status === "failed" && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <AlertCircle className="h-5 w-5 text-red-500" />
                    Generation Failed
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-sm text-muted-foreground">
                    {currentGeneration.error || "Something went wrong during generation."}
                  </p>
                  <Button onClick={resetGeneration} variant="outline">
                    Try Again
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Recent Generations */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <History className="h-5 w-5" />
                  Recent Generations
                </CardTitle>
              </CardHeader>
              <CardContent>
                {recentGenerations.length > 0 ? (
                  <div className="space-y-4">
                    {recentGenerations.map((generation) => {
                      const trackUrl = generation.track ? generateTrackUrl({
                        prompt: generation.prompt,
                        title: generation.track.title,
                        bpm: generation.track.bpm,
                        uuid: generation.track.uuid,
                        style: generation.track.style,
                        slug: generation.track.slug
                      }) : null;

                      return (
                        <div
                          key={generation.generation_uuid}
                          className={cn(
                            "flex items-center justify-between p-3 border rounded-lg transition-colors",
                            generation.status === "completed" && trackUrl
                              ? "hover:bg-muted/50 cursor-pointer"
                              : ""
                          )}
                          onClick={() => {
                            if (generation.status === "completed" && trackUrl) {
                              router.push(trackUrl);
                            }
                          }}
                        >
                          <div className="space-y-1 flex-1">
                            <div className="font-medium text-sm">
                              {generation.track?.title || generation.prompt || "Untitled"}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {generation.track?.style && `${generation.track.style} • `}
                              {generation.track?.duration && `${generation.track.duration}s`}
                              {generation.track?.bpm && ` • ${generation.track.bpm} BPM`}
                            </div>
                            {generation.status === "completed" && trackUrl && (
                              <div className="text-xs text-primary">
                                Click to view details →
                              </div>
                            )}
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge
                              variant={
                                generation.status === "completed"
                                  ? "default"
                                  : generation.status === "failed"
                                  ? "destructive"
                                  : "secondary"
                              }
                            >
                              {generation.status}
                            </Badge>
                            {generation.track && generation.status === "completed" && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  // TODO: Implement quick play functionality
                                  toast.info("Quick play coming soon!");
                                }}
                              >
                                <Play className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <Music className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No recent generations.</p>
                    <p className="text-sm">Start creating your first music loop!</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Tips Card */}
            <Card>
              <CardHeader>
                <CardTitle>💡 Generation Tips</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3 text-sm">
                <div>
                  <strong>Be specific:</strong> Describe the mood, instruments, and style you want.
                </div>
                <div>
                  <strong>Use BPM lock:</strong> For precise tempo matching with your content.
                </div>
                <div>
                  <strong>Test loops:</strong> Use the loop test player to verify seamless playback.
                </div>
                <div>
                  <strong>Choose duration:</strong> 15s for short clips, 60s for longer content.
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
