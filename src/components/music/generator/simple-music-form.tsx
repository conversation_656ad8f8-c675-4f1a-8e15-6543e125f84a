"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { TechButton } from "@/components/ui/tech-button";
import { Tech<PERSON>ard, TechCardContent } from "@/components/ui/tech-card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Loader2, Music, Sparkles, Clock, Zap } from "lucide-react";
import { toast } from "sonner";
import { cn } from "@/lib/utils";

const musicFormSchema = z.object({
  prompt: z.string().min(10, "Please describe your music idea").max(500, "Description too long"),
  duration: z.enum(["15", "30", "60"]),
  bpm: z.number().min(60).max(200).optional(),
});

type MusicFormValues = z.infer<typeof musicFormSchema>;

interface SimpleMusicFormProps {
  onSubmit: (values: MusicFormValues) => Promise<void>;
  isLoading?: boolean;
  userCredits?: number;
}

const DURATION_OPTIONS = [
  { value: "15", label: "15s", credits: 1, description: "Quick loop" },
  { value: "30", label: "30s", credits: 2, description: "Standard" },
  { value: "60", label: "60s", credits: 3, description: "Extended" },
];

const BPM_PRESETS = [
  { value: 70, label: "Slow", description: "Chill, ambient" },
  { value: 90, label: "Medium", description: "Pop, rock" },
  { value: 120, label: "Standard", description: "Dance, electronic" },
  { value: 140, label: "Fast", description: "Techno, EDM" },
  { value: 160, label: "Very Fast", description: "Drum & bass" },
];

export default function SimpleMusicForm({ onSubmit, isLoading = false, userCredits = 0 }: SimpleMusicFormProps) {
  const [selectedBpm, setSelectedBpm] = useState<number | undefined>(undefined);

  const form = useForm<MusicFormValues>({
    resolver: zodResolver(musicFormSchema),
    defaultValues: {
      prompt: "",
      duration: "30",
      bpm: undefined,
    },
  });

  const watchedDuration = form.watch("duration");
  const selectedDurationOption = DURATION_OPTIONS.find(opt => opt.value === watchedDuration);
  const requiredCredits = selectedDurationOption?.credits || 2;
  const canGenerate = userCredits >= requiredCredits;

  const handleSubmit = async (values: MusicFormValues) => {
    if (!canGenerate) {
      toast.error(`Need ${requiredCredits} credits, you have ${userCredits}`);
      return;
    }

    try {
      await onSubmit(values);
      toast.success("Music generation started!");
      form.reset();
      setSelectedBpm(undefined);
    } catch (error) {
      toast.error("Failed to start generation");
      console.error("Generation error:", error);
    }
  };

  const selectBpm = (bpm: number) => {
    setSelectedBpm(bpm);
    form.setValue("bpm", bpm);
  };

  const clearBpm = () => {
    setSelectedBpm(undefined);
    form.setValue("bpm", undefined);
  };

  return (
    <TechCard variant="glass" className="w-full max-w-2xl mx-auto">
      <TechCardContent className="p-8">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-8">
            {/* Music Description */}
            <FormField
              control={form.control}
              name="prompt"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-lg font-medium">What music do you want?</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Upbeat electronic music for workout videos..."
                      className="min-h-[120px] text-base resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Duration Selection */}
            <FormField
              control={form.control}
              name="duration"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-lg font-medium">Duration</FormLabel>
                  <div className="grid grid-cols-3 gap-3">
                    {DURATION_OPTIONS.map((option) => (
                      <button
                        key={option.value}
                        type="button"
                        onClick={() => field.onChange(option.value)}
                        className={cn(
                          "p-4 rounded-lg border-2 transition-all duration-200 text-center",
                          field.value === option.value
                            ? "border-primary bg-primary/10 text-primary"
                            : "border-border hover:border-primary/50 hover:bg-muted/50"
                        )}
                      >
                        <div className="font-semibold text-lg">{option.label}</div>
                        <div className="text-sm text-muted-foreground">{option.description}</div>
                        <div className="flex items-center justify-center gap-1 mt-2">
                          <Zap className="h-3 w-3" />
                          <span className="text-xs">{option.credits}</span>
                        </div>
                      </button>
                    ))}
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* BPM Selection */}
            <FormField
              control={form.control}
              name="bpm"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-lg font-medium">
                    Tempo (Optional)
                    {selectedBpm && (
                      <button
                        type="button"
                        onClick={clearBpm}
                        className="ml-2 text-sm text-primary hover:underline"
                      >
                        Clear
                      </button>
                    )}
                  </FormLabel>
                  <div className="grid grid-cols-2 md:grid-cols-5 gap-2">
                    {BPM_PRESETS.map((preset) => (
                      <button
                        key={preset.value}
                        type="button"
                        onClick={() => selectBpm(preset.value)}
                        className={cn(
                          "p-3 rounded-lg border transition-all duration-200 text-center",
                          selectedBpm === preset.value
                            ? "border-primary bg-primary/10 text-primary"
                            : "border-border hover:border-primary/50 hover:bg-muted/50"
                        )}
                      >
                        <div className="font-medium">{preset.label}</div>
                        <div className="text-xs text-muted-foreground">{preset.value} BPM</div>
                      </button>
                    ))}
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Leave empty for automatic tempo detection
                  </p>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Cost and Generate Button */}
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-muted/50 rounded-lg">
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">Cost: {requiredCredits} credits</span>
                </div>
                <div className="flex items-center gap-2">
                  <Zap className="h-4 w-4 text-primary" />
                  <span className={cn(
                    "text-sm font-medium",
                    canGenerate ? "text-green-600" : "text-red-600"
                  )}>
                    {userCredits} available
                  </span>
                </div>
              </div>

              <TechButton
                type="submit"
                variant="neon"
                size="xl"
                className="w-full"
                loading={isLoading}
                disabled={!canGenerate}
              >
                {isLoading ? (
                  "Generating..."
                ) : (
                  <>
                    <Sparkles className="mr-2 h-5 w-5" />
                    Generate Music
                  </>
                )}
              </TechButton>

              {!canGenerate && (
                <p className="text-sm text-red-600 text-center">
                  Need more credits to generate music
                </p>
              )}
            </div>
          </form>
        </Form>
      </TechCardContent>
    </TechCard>
  );
}
