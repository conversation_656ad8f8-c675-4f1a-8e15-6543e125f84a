/**
 * Volcengine 音乐生成服务提供商
 */

import { BaseMusicProvider } from "./base-music-provider";
import { MusicProviderConfig, MusicGenerationOptions } from "@/types/music";
import { sign, hash, getDateTimeNow } from "@/lib/volcengine-signature";

export interface VolcanoMusicRequest {
  Text: string;
  Duration?: number;
  Genre?: string[];
  Mood?: string[];
  Instrument?: string[];
  Theme?: string[];
  CallbackURL?: string;
}

export interface VolcanoMusicResponse {
  Code: number;
  Message: string;
  Result: {
    TaskID: string;
  };
  ResponseMetadata: {
    RequestId: string;
    Action: string;
    Version: string;
    Service: string;
    Region: string;
    Error: any;
  };
}

export interface VolcanoQueryResponse {
  Code: number;
  Message: string;
  Result: {
    TaskID: string;
    Status: number; // 0->等待中, 1->处理中, 2->成功, 3->失败
    Progress: number;
    FailureReason?: {
      Code: number;
      Msg: string;
    };
    SongDetail?: {
      AudioUrl: string;
      Prompt: string;
      Duration: number;
      CallbackURL: string;
      Theme: string;
      Instrument: string;
      Mood: string;
      Genre: string;
      TosPath: string;
    };
  };
}

export class VolcanoProvider extends BaseMusicProvider {
  private accessKeyId: string;
  private secretAccessKey: string;
  private region: string;
  private serviceName: string;
  private baseUrl: string;
  private version: string;

  constructor(config: MusicProviderConfig & {
    access_key_id: string;
    secret_access_key: string;
    region?: string;
    service_name?: string;
    version?: string;
  }) {
    super("volcano", config);
    
    this.accessKeyId = config.access_key_id;
    this.secretAccessKey = config.secret_access_key;
    this.region = config.region || "cn-beijing";
    this.serviceName = config.service_name || "imagination";
    this.baseUrl = config.base_url || "https://open.volcengineapi.com";
    this.version = config.version || "2024-08-12";

    if (!this.accessKeyId || !this.secretAccessKey) {
      throw new Error("Volcano API credentials are required");
    }
  }

  protected getDisplayName(): string {
    return "Volcengine Music";
  }

  protected getSupportedDurations(): number[] {
    return [15, 30, 60];
  }

  protected getSupportedStyles(): string[] {
    return [
      "corporate", "dance/edm", "orchestral", "chill out", "rock", "hip hop",
      "folk", "funk", "ambient", "holiday", "jazz", "kids", "world", "travel",
      "commercial", "advertising", "driving", "cinematic", "upbeat", "epic",
      "inspiring", "business", "video game", "dark", "pop", "trailer",
      "modern", "electronic", "documentary", "soundtrack", "fashion",
      "acoustic", "movie", "tv", "high tech", "industrial", "dance",
      "video", "vlog", "marketing", "game", "radio", "promotional",
      "sports", "party", "summer", "beauty"
    ];
  }

  protected getMaxBpm(): number {
    return 200;
  }

  protected getMinBpm(): number {
    return 60;
  }

  protected supportsStems(): boolean {
    return false;
  }

  protected supportsVariations(): boolean {
    return false;
  }

  /**
   * 发送API请求
   */
  private async makeRequest(action: string, body: any = {}, method: string = "POST"): Promise<any> {
    const query = {
      Action: action,
      Version: this.version,
    };

    // 正规化 query object - 参考工作代码
    for (const [key, val] of Object.entries(query)) {
      if (val === undefined || val === null) {
        query[key] = '';
      }
    }

    const requestBody = method === "POST" ? JSON.stringify(body) : "";
    const headers = {
      "X-Date": getDateTimeNow(),
      "Content-Type": "application/json",
      "Host": "open.volcengineapi.com",
    };

    const signParams = {
      headers,
      method,
      query,
      accessKeyId: this.accessKeyId,
      secretAccessKey: this.secretAccessKey,
      serviceName: this.serviceName,
      region: this.region,
      body: requestBody,
      bodySha: hash(requestBody),
    };

    const authorization = sign(signParams);

    // 使用 qs.stringify 与工作代码保持一致
    const qs = await import('querystring');
    const queryString = qs.stringify(query);
    const url = `${this.baseUrl}/?${queryString}`;

    console.log("Volcano API request:", {
      url,
      method,
      headers: { ...headers, Authorization: authorization.substring(0, 50) + "..." },
      body: requestBody
    });

    try {
      const response = await fetch(url, {
        method,
        headers: {
          ...headers,
          Authorization: authorization,
        },
        body: method === "POST" ? requestBody : undefined,
      });

      const responseText = await response.text();
      console.log("Volcano API response:", {
        status: response.status,
        statusText: response.statusText,
        body: responseText
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${responseText}`);
      }

      const result = JSON.parse(responseText);

      if (result.Code !== 0) {
        throw new Error(`Volcano API Error ${result.Code}: ${result.Message}`);
      }

      return result;
    } catch (error) {
      console.error('Volcano API error:', error);
      throw error;
    }
  }

  /**
   * 映射参数到Volcano API格式
   */
  private mapOptionsToVolcano(options?: MusicGenerationOptions): Partial<VolcanoMusicRequest> {
    const volcanoParams: Partial<VolcanoMusicRequest> = {};

    if (options?.style) {
      volcanoParams.Genre = Array.isArray(options.style) ? options.style : [options.style];
    }

    if (options?.mood) {
      volcanoParams.Mood = Array.isArray(options.mood) ? options.mood : [options.mood];
    }

    // 可以根据需要添加更多参数映射
    return volcanoParams;
  }

  async generateMusic(
    prompt: string,
    duration: number,
    options?: MusicGenerationOptions
  ): Promise<{ task_id: string; estimated_time: number }> {
    try {
      const volcanoParams: VolcanoMusicRequest = {
        Text: prompt,
        Duration: Math.min(Math.max(duration, 1), 60), // 限制在1-60秒范围内
        ...this.mapOptionsToVolcano(options),
      };

      console.log("Volcano generation request:", volcanoParams);

      const response: VolcanoMusicResponse = await this.makeRequest("GenBGM", volcanoParams);

      return {
        task_id: response.Result.TaskID,
        estimated_time: duration * 2, // 估计生成时间为音频时长的2倍
      };
    } catch (error) {
      console.error("Volcano generation failed:", error);
      throw new Error(`Failed to start Volcano generation: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  async checkStatus(task_id: string): Promise<{
    status: "pending" | "processing" | "completed" | "failed";
    progress?: number;
    result?: { file_url: string; file_size: number; metadata?: any };
    error?: string;
  }> {
    try {
      const response: VolcanoQueryResponse = await this.makeRequest("QuerySong", {
        TaskID: task_id,
      });

      const result = response.Result;
      const status = this.mapVolcanoStatus(result.Status);

      const statusResult: any = {
        status,
        progress: result.Progress || 0,
      };

      if (status === "completed" && result.SongDetail) {
        statusResult.result = {
          file_url: result.SongDetail.AudioUrl,
          file_size: 0, // Volcano API 不提供文件大小信息
          metadata: {
            duration: result.SongDetail.Duration,
            prompt: result.SongDetail.Prompt,
            genre: result.SongDetail.Genre,
            mood: result.SongDetail.Mood,
            theme: result.SongDetail.Theme,
            instrument: result.SongDetail.Instrument,
          },
        };
      } else if (status === "failed" && result.FailureReason) {
        statusResult.error = `${result.FailureReason.Code}: ${result.FailureReason.Msg}`;
      }

      return statusResult;
    } catch (error) {
      console.error("Volcano status check failed:", error);
      throw new Error(`Failed to check Volcano status: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  async cancelGeneration(task_id: string): Promise<boolean> {
    // Volcengine API 目前不支持取消任务，返回 false
    console.log(`Volcano provider does not support cancellation for task: ${task_id}`);
    return false;
  }

  /**
   * 映射Volcano状态到标准状态
   */
  private mapVolcanoStatus(volcanoStatus: number): "pending" | "processing" | "completed" | "failed" {
    switch (volcanoStatus) {
      case 0:
        return "pending";
      case 1:
        return "processing";
      case 2:
        return "completed";
      case 3:
        return "failed";
      default:
        return "pending";
    }
  }

  /**
   * 获取支持的参数选项
   */
  static getSupportedOptions() {
    return {
      genres: [
        "corporate", "dance/edm", "orchestral", "chill out", "rock", "hip hop",
        "folk", "funk", "ambient", "holiday", "jazz", "kids", "world", "travel",
        "commercial", "advertising", "driving", "cinematic", "upbeat", "epic",
        "inspiring", "business", "video game", "dark", "pop", "trailer",
        "modern", "electronic", "documentary", "soundtrack", "fashion",
        "acoustic", "movie", "tv", "high tech", "industrial", "dance",
        "video", "vlog", "marketing", "game", "radio", "promotional",
        "sports", "party", "summer", "beauty"
      ],
      moods: [
        "positive", "uplifting", "energetic", "happy", "bright", "optimistic",
        "hopeful", "cool", "dreamy", "fun", "light", "powerful", "calm",
        "confident", "joyful", "dramatic", "peaceful", "playful", "soft",
        "groovy", "reflective", "easy", "relaxed", "lively", "smooth",
        "romantic", "intense", "elegant", "mellow", "emotional", "sentimental",
        "cheerful happy", "contemplative", "soothing", "proud", "passionate",
        "sweet", "mystical", "tranquil", "cheerful", "casual", "beautiful",
        "ethereal", "melancholy", "sad", "aggressive", "haunting",
        "adventure", "serene", "sincere", "funky", "funny"
      ],
      instruments: [
        "piano", "drums", "guitar", "percussion", "synth", "electric guitar",
        "acoustic guitar", "bass guitar", "brass", "violin", "cello", "flute",
        "organ", "trumpet", "ukulele", "saxophone", "double bass", "harp",
        "glockenspiel", "synthesizer", "keyboard", "marimba", "bass", "banjo", "strings"
      ],
      themes: [
        "inspirational", "motivational", "achievement", "discovery", "every day",
        "love", "technology", "lifestyle", "journey", "meditation", "drama",
        "children", "hope", "fantasy", "holiday", "health", "family",
        "real estate", "media", "kids", "science", "education", "progress",
        "world", "vacation", "training", "christmas", "sales"
      ]
    };
  }
}
