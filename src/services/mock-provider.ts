import { BaseMusicProvider, MusicGenerationResult, MusicProviderInfo } from "./base-music-provider";
import { MusicGenerationOptions } from "@/types/music";

/**
 * 模拟音乐提供商 - 用于测试和开发
 */
export class MockProvider extends BaseMusicProvider {
  constructor(config: any) {
    super(config);
  }

  getProviderInfo(): MusicProviderInfo {
    return {
      name: "mock",
      display_name: "Mock Provider",
      description: "Mock music provider for testing",
      supported_durations: [15, 30, 60],
      supported_styles: [
        "ambient", "corporate", "electronic", "acoustic", "cinematic",
        "upbeat", "chill", "jazz", "classical", "rock", "pop"
      ],
      supported_moods: [
        "peaceful", "energetic", "happy", "calm", "dramatic",
        "romantic", "mysterious", "uplifting", "relaxed", "powerful"
      ],
      features: ["Fast Generation", "High Quality", "Multiple Styles"]
    };
  }

  async generateMusic(
    prompt: string,
    duration: number,
    options: MusicGenerationOptions
  ): Promise<MusicGenerationResult> {
    console.log("Mock provider generating music:", {
      prompt,
      duration,
      options
    });

    // 模拟生成过程
    const taskId = `mock_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // 模拟异步生成
    setTimeout(async () => {
      try {
        // 模拟生成完成，创建一个简单的音频文件 URL
        const audioUrl = `https://example.com/mock-audio/${taskId}.wav`;
        
        // 更新数据库状态为完成
        const { updateMusicGeneration } = await import("@/models/music-generation");
        await updateMusicGeneration(taskId, {
          status: "completed",
          audio_url: audioUrl,
          file_size: 1024 * 1024, // 1MB
          completed_at: new Date(),
        });

        console.log(`Mock generation completed: ${taskId}`);
      } catch (error) {
        console.error(`Mock generation failed: ${taskId}`, error);
        
        // 更新状态为失败
        const { updateMusicGeneration } = await import("@/models/music-generation");
        await updateMusicGeneration(taskId, {
          status: "failed",
          error_message: "Mock generation failed",
        });
      }
    }, 5000); // 5秒后完成

    return {
      task_id: taskId,
      status: "pending",
      estimated_time: 5, // 5秒
      message: "Mock music generation started"
    };
  }

  async checkStatus(taskId: string): Promise<any> {
    console.log("Mock provider checking status:", taskId);
    
    // 模拟状态检查
    return {
      status: "pending",
      progress: Math.floor(Math.random() * 100),
      message: "Mock generation in progress"
    };
  }

  async getResult(taskId: string): Promise<any> {
    console.log("Mock provider getting result:", taskId);
    
    // 模拟结果获取
    return {
      status: "completed",
      audio_url: `https://example.com/mock-audio/${taskId}.wav`,
      duration: 30,
      file_size: 1024 * 1024
    };
  }

  isConfigured(): boolean {
    // Mock 提供商总是可用的
    return true;
  }

  getDisplayName(): string {
    return "Mock Provider";
  }

  getSupportedDurations(): number[] {
    return [15, 30, 60];
  }

  getSupportedStyles(): string[] {
    return [
      "ambient", "corporate", "electronic", "acoustic", "cinematic",
      "upbeat", "chill", "jazz", "classical", "rock", "pop"
    ];
  }

  getSupportedMoods(): string[] {
    return [
      "peaceful", "energetic", "happy", "calm", "dramatic",
      "romantic", "mysterious", "uplifting", "relaxed", "powerful"
    ];
  }
}
