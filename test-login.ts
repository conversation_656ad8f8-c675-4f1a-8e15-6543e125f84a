import 'dotenv/config';
import { config } from 'dotenv';
import { saveUser } from './src/services/user';
import { User } from './src/types/user';
import { getUuid } from './src/lib/hash';

// Load environment variables
config({ path: '.env.development' });
config({ path: '.env.local' });
config({ path: '.env' });

async function testGoogleLogin() {
  try {
    console.log('Testing Google login flow...');
    
    // Simulate a Google user
    const testUser: User = {
      uuid: getUuid(),
      email: '<EMAIL>',
      nickname: 'Test User',
      avatar_url: 'https://example.com/avatar.jpg',
      signin_type: 'oauth',
      signin_provider: 'google',
      signin_openid: '123456789',
      created_at: new Date(),
      signin_ip: '127.0.0.1',
    };

    console.log('Test user data:', testUser);
    
    // Try to save the user (this should trigger the error if it exists)
    const savedUser = await saveUser(testUser);
    
    console.log('✅ User saved successfully!');
    console.log('Saved user:', savedUser);
    
  } catch (error) {
    console.error('❌ Error during login test:', error);
    console.error('Error details:', error.message);
    console.error('Stack trace:', error.stack);
  }
  
  process.exit(0);
}

testGoogleLogin();
