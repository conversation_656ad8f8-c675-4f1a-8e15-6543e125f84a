import 'dotenv/config';
import { config } from 'dotenv';
import { findUserByEmail, findUserByUuid } from './src/models/user';
import { getUserInfo } from './src/services/user';

// Load environment variables
config({ path: '.env.development' });
config({ path: '.env.local' });
config({ path: '.env' });

async function testUserQueries() {
  try {
    console.log('Testing user queries...');
    
    // Test 1: Find user by email
    console.log('\n1. Testing findUserByEmail...');
    const userByEmail = await findUserByEmail('<EMAIL>');
    console.log('User by email:', userByEmail);
    
    if (userByEmail) {
      // Test 2: Find user by UUID
      console.log('\n2. Testing findUserByUuid...');
      const userByUuid = await findUserByUuid(userByEmail.uuid);
      console.log('User by UUID:', userByUuid);
      
      // Test 3: Test getUserInfo service
      console.log('\n3. Testing getUserInfo service...');
      // This would require a session, so we'll skip it for now
      console.log('Skipping getUserInfo (requires session)');
    }
    
    console.log('\n✅ All user queries completed successfully!');
    
  } catch (error) {
    console.error('❌ Error during user query test:', error);
    console.error('Error details:', error.message);
    console.error('Stack trace:', error.stack);
  }
  
  process.exit(0);
}

testUserQueries();
